# 🏠 Homeserver Monitoring Dashboard

A comprehensive Grafana-based monitoring solution for your homeserver, tracking all Docker containers and system resources.

## 🚀 Features

- **System Monitoring**: CPU, Memory, Disk, Network usage
- **Docker Container Tracking**: All running containers including Jellyfin, FileBrowser, qBittorrent, etc.
- **Application Health**: Service status and uptime monitoring
- **Beautiful Dashboards**: Pre-configured dashboards with homeserver-specific metrics
- **Real-time Updates**: Live monitoring with 5-30 second refresh rates

## 📊 Dashboards

### 1. **Homeserver Dashboard** (`homeserver-dashboard.json`)
- System resource gauges (CPU, Memory, Disk)
- Network I/O monitoring
- Container status table
- Container resource usage (CPU, Memory, Network)

### 2. **Homeserver Applications** (`homeserver-apps.json`)
- Service status overview with emoji indicators
- Online/Offline service counts
- Docker container count
- System uptime display

## 🛠️ Services

| Service | Port | Purpose |
|---------|------|---------|
| **Grafana** | 3000 | Main dashboard interface |
| **Prometheus** | 9091 | Metrics collection and storage |
| **Node Exporter** | 9100 | System metrics collection |
| **cAdvisor** | 8082 | Docker container metrics |

## 🚀 Quick Start

1. **Start the monitoring stack:**
   ```bash
   cd developement/grafana
   docker compose up -d
   ```

2. **Access Grafana:**
   - URL: http://localhost:3000
   - Username: `admin`
   - Password: `admin123`

3. **View your dashboards:**
   - Navigate to "Dashboards" → "Browse"
   - Select "🏠 Homeserver Dashboard" or "🏠 Homeserver Applications"

## 📈 Monitored Applications

Your homeserver setup includes monitoring for:

- 🎬 **Jellyfin** (Media Server)
- 📁 **FileBrowser** (File Management)
- 🌊 **qBittorrent** (Torrent Client)
- 🐳 **Portainer** (Docker Management)
- 🏠 **Homarr** (Homepage Dashboard)
- 🎮 **RomM** (ROM Management)
- 👁️ **Glances** (System Monitor)
- 💰 **MoneyTracker** (Personal Finance)

## 🔧 Configuration Files

- `grafana.ini` - Grafana server configuration
- `prometheus.yml` - Metrics collection configuration
- `docker-compose.yml` - Service orchestration
- `provisioning/` - Auto-provisioning configs
- `dashboards/` - Dashboard definitions

## 📊 Key Metrics

### System Metrics
- CPU usage percentage
- Memory utilization
- Disk space usage
- Network I/O rates

### Container Metrics
- Container status (running/stopped)
- CPU usage per container
- Memory consumption per container
- Network traffic per container

## 🎨 Dashboard Features

- **Dark Theme**: Easy on the eyes for 24/7 monitoring
- **Auto-refresh**: Real-time updates every 5-30 seconds
- **Responsive Design**: Works on desktop and mobile
- **Color-coded Status**: Green for healthy, red for issues
- **Historical Data**: 200 hours of metric retention

## 🔍 Troubleshooting

### Services Not Starting
```bash
# Check service status
docker compose ps

# View logs
docker compose logs grafana
docker compose logs prometheus
```

### No Data in Dashboards
1. Verify Prometheus is scraping targets: http://localhost:9091/targets
2. Check if node-exporter is accessible: http://localhost:9100/metrics
3. Verify cAdvisor is running: http://localhost:8082/metrics

### Port Conflicts
If you encounter port conflicts, modify the ports in `docker-compose.yml`:
```yaml
ports:
  - "3001:3000"  # Change Grafana port
  - "9092:9090"  # Change Prometheus port
```

## 🔄 Updates

To update the monitoring stack:
```bash
docker compose pull
docker compose up -d
```

## 📝 Customization

### Adding New Services
1. Add scrape config to `prometheus.yml`
2. Update dashboard queries to include new services
3. Restart the stack: `docker compose restart`

### Custom Dashboards
- Import dashboards from Grafana.com
- Create custom panels using PromQL queries
- Export and save to `dashboards/` directory

---

**🎯 Your homeserver is now fully monitored!** Access Grafana at http://localhost:3000 and enjoy comprehensive insights into your system's performance.
