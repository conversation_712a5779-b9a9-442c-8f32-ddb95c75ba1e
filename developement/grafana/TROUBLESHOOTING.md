# 🔧 Homeserver Dashboard Troubleshooting

## ❌ "Data source Prometheus not found" Error

### Quick Fix:
1. **Go to Grafana:** http://localhost:3000
2. **Login:** admin / admin123
3. **Go to Configuration → Data Sources**
4. **Click "Add data source"**
5. **Select "Prometheus"**
6. **Set URL:** `http://prometheus:9090`
7. **Click "Save & Test"**

### Alternative Fix:
If the datasource exists but dashboards can't find it:
1. Go to **Configuration → Data Sources**
2. Click on the **Prometheus** datasource
3. Note the **UID** (should be "prometheus")
4. If different, update dashboard JSON files

## 🔍 Checking Service Status

### Verify all services are running:
```bash
cd developement/grafana
docker compose ps
```

### Check Prometheus targets:
- Go to: http://localhost:9091/targets
- Should show:
  - ✅ prometheus (localhost:9090)
  - ✅ node-exporter (node-exporter:9100)  
  - ✅ cadvisor (cadvisor:8080)

### Check metrics are being collected:
```bash
# Test Prometheus API
curl "http://localhost:9091/api/v1/query?query=up"

# Test node-exporter metrics
curl http://localhost:9100/metrics | head -10

# Test cAdvisor metrics  
curl http://localhost:8082/metrics | head -10
```

## 📊 Dashboard Issues

### No Data in Panels:
1. **Check time range** - Set to "Last 5 minutes" or "Last 1 hour"
2. **Verify queries** - Go to panel edit mode and check if queries return data
3. **Check datasource** - Ensure "Prometheus" is selected

### Panels Show "N/A":
- Wait 1-2 minutes for metrics to be collected
- Refresh the dashboard (Ctrl+R)
- Check if the metric exists in Prometheus: http://localhost:9091/graph

## 🚀 Available Dashboards

1. **🏠 Homeserver Simple Dashboard** - Basic system metrics
2. **System Overview** - Original system dashboard  
3. **🏠 Homeserver Applications** - Service status overview

## 🔄 Restart Services

If issues persist, restart the monitoring stack:
```bash
cd developement/grafana
docker compose restart
```

Or restart individual services:
```bash
docker compose restart grafana
docker compose restart prometheus
```

## 📈 Key Metrics Available

### System Metrics (from node-exporter):
- `node_cpu_seconds_total` - CPU usage
- `node_memory_MemTotal_bytes` - Total memory
- `node_memory_MemAvailable_bytes` - Available memory
- `node_filesystem_size_bytes` - Disk size
- `node_filesystem_avail_bytes` - Available disk space
- `node_network_receive_bytes_total` - Network RX
- `node_network_transmit_bytes_total` - Network TX

### Container Metrics (from cAdvisor):
- `container_last_seen` - Container status
- `container_cpu_usage_seconds_total` - Container CPU
- `container_memory_usage_bytes` - Container memory
- `container_network_receive_bytes_total` - Container network RX
- `container_network_transmit_bytes_total` - Container network TX

## 🆘 Still Having Issues?

1. **Check logs:**
   ```bash
   docker compose logs grafana
   docker compose logs prometheus
   ```

2. **Verify network connectivity:**
   ```bash
   docker exec grafana ping prometheus
   docker exec prometheus ping node-exporter
   ```

3. **Reset everything:**
   ```bash
   docker compose down
   docker compose up -d
   ```

4. **Manual datasource setup:**
   - Go to Grafana → Configuration → Data Sources
   - Add Prometheus manually with URL: `http://prometheus:9090`

---

**💡 Tip:** The "🏠 Homeserver Simple Dashboard" should work immediately after fixing the datasource issue!
