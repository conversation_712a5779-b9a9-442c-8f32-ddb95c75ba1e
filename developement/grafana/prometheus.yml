global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # The job name is added as a label `job=<job_name>` to any timeseries scraped from this config.
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']

  # Docker daemon metrics (if enabled)
  - job_name: 'docker'
    static_configs:
      - targets: ['host.docker.internal:9323']
    scrape_interval: 30s

  # Glances metrics (system monitoring)
  - job_name: 'glances'
    static_configs:
      - targets: ['glances:61208']
    metrics_path: '/api/3/prometheus'
    scrape_interval: 10s

  # Add cAdvisor for container metrics
  - job_name: 'cadvisor'
    static_configs:
      - targets: ['cadvisor:8080']
    scrape_interval: 15s

  # Homeserver applications health checks
  - job_name: 'jellyfin'
    static_configs:
      - targets: ['jellyfin:8096']
    metrics_path: '/health'
    scrape_interval: 30s

  - job_name: 'portainer'
    static_configs:
      - targets: ['portainer:9000']
    scrape_interval: 30s
