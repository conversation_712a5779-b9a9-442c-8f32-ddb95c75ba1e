#!/bin/bash

# Wait for <PERSON><PERSON> to be ready
echo "Waiting for <PERSON><PERSON> to be ready..."
until curl -s http://localhost:3000/api/health > /dev/null; do
    echo "Waiting for <PERSON><PERSON>..."
    sleep 2
done

echo "<PERSON><PERSON> is ready! Setting up Prometheus datasource..."

# Create Prometheus datasource
curl -X POST \
  -H "Content-Type: application/json" \
  -u admin:admin123 \
  http://localhost:3000/api/datasources \
  -d '{
    "name": "Prometheus",
    "type": "prometheus",
    "url": "http://prometheus:9090",
    "access": "proxy",
    "isDefault": true,
    "uid": "prometheus"
  }'

echo ""
echo "✅ Datasource setup complete!"
echo "🌐 Access Grafana at: http://localhost:3000"
echo "👤 Username: admin"
echo "🔑 Password: admin123"
echo ""
echo "📊 Available dashboards:"
echo "  - 🏠 Homeserver Simple Dashboard"
echo "  - System Overview"
echo "  - 🏠 Homeserver Applications"
