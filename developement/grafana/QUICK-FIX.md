# 🚀 Quick Fix for "Data source Prometheus not found"

## ✅ **The Issue is Fixed!**

After restarting the stack, the datasource should now be working. Here's how to verify and fix if needed:

## 🔧 **Step 1: Access Grafana**

1. **Open Grafana:** http://localhost:3000
2. **Login:**
   - Username: `admin`
   - Password: `admin123`

## 🔧 **Step 2: Verify Datasource (if still having issues)**

If you still see "Data source Prometheus not found":

1. **Go to Configuration → Data Sources**
2. **If no Prometheus datasource exists:**
   - Click **"Add data source"**
   - Select **"Prometheus"**
   - Set **Name:** `Prometheus`
   - Set **URL:** `http://prometheus:9090`
   - Click **"Save & Test"**

3. **If Prometheus datasource exists but has different name:**
   - Note the exact name (e.g., "prometheus", "Prometheus")
   - Edit the dashboard and change datasource to match

## 🔧 **Step 3: Test Your Dashboard**

1. **Go to Dashboards → Browse**
2. **Select "🏠 Homeserver Simple Dashboard"**
3. **You should see:**
   - ✅ CPU Usage gauge
   - ✅ Memory Usage gauge  
   - ✅ Disk Usage gauge
   - ✅ Docker Container count
   - ✅ Network I/O graph
   - ✅ Container CPU usage graph

## 🔧 **Step 4: If Dashboard Still Shows "N/A"**

1. **Check time range** - Set to "Last 5 minutes"
2. **Wait 1-2 minutes** for metrics to be collected
3. **Refresh the page** (Ctrl+R)

## 🔧 **Step 5: Manual Datasource Setup (if needed)**

Run this command to manually create the datasource:

```bash
cd developement/grafana
./setup-datasource.sh
```

## 🔧 **Step 6: Verify Metrics Are Being Collected**

Check these URLs to ensure metrics are available:

- **Prometheus targets:** http://localhost:9091/targets
- **Node metrics:** http://localhost:9100/metrics
- **Container metrics:** http://localhost:8082/metrics

## 📊 **Expected Results**

Your dashboard should show:
- **CPU Usage:** Real-time percentage
- **Memory Usage:** RAM consumption with color thresholds
- **Disk Usage:** Storage space with warnings
- **Docker Containers:** Count of running containers
- **Network I/O:** Traffic graphs over time
- **Container CPU:** Per-container CPU usage

## 🆘 **Still Not Working?**

1. **Restart the stack:**
   ```bash
   cd developement/grafana
   docker compose restart
   ```

2. **Check logs:**
   ```bash
   docker compose logs grafana
   docker compose logs prometheus
   ```

3. **Verify all services are running:**
   ```bash
   docker compose ps
   ```

## ✨ **Success Indicators**

✅ All services show "Up" status  
✅ Prometheus targets are "UP" at http://localhost:9091/targets  
✅ Grafana loads without datasource errors  
✅ Dashboard panels show real data instead of "N/A"  

---

**🎯 Your homeserver monitoring should now be fully functional!**
